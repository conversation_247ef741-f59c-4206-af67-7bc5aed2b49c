<?php
require_once 'config/database.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "Checking product attributes...\n\n";
    
    // Check what products have attributes
    $stmt = $conn->query('SELECT DISTINCT product_id, name FROM attributes LIMIT 10');
    $attributes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Available product attributes:\n";
    foreach($attributes as $attr) {
        echo $attr['product_id'] . ' - ' . $attr['name'] . "\n";
    }
    
    echo "\n\nChecking specific product attributes:\n";
    
    // Check attributes for a specific product
    $stmt = $conn->prepare("
        SELECT attributes.name, attribute_items.value, attribute_items.display_value
        FROM attributes 
        JOIN attribute_items ON attributes.id = attribute_items.attribute_id 
        WHERE attributes.product_id = 'ps-5'
    ");
    $stmt->execute();
    $psAttributes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "PS-5 attributes:\n";
    foreach($psAttributes as $attr) {
        echo "  " . $attr['name'] . ": " . $attr['value'] . " (" . $attr['display_value'] . ")\n";
    }
    
    // Check what products are in cart
    echo "\n\nProducts in cart:\n";
    $stmt = $conn->query('SELECT cart.id, cart.product_id, products.name FROM cart JOIN products ON cart.product_id = products.id');
    $cartItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach($cartItems as $item) {
        echo "Cart ID: " . $item['id'] . " - Product: " . $item['product_id'] . " (" . $item['name'] . ")\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
