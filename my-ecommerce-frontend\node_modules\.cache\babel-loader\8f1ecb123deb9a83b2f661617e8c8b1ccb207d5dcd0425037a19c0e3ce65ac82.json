{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\NetworkTest.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction NetworkTest() {\n  _s();\n  const [testResults, setTestResults] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const addResult = (message, type = 'info') => {\n    setTestResults(prev => [...prev, {\n      message,\n      type,\n      timestamp: new Date().toLocaleTimeString()\n    }]);\n  };\n  const testDirectFetch = async () => {\n    setIsLoading(true);\n    addResult('Testing direct fetch to GraphQL endpoint...', 'info');\n    try {\n      const response = await fetch('https://localhost:8000', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Accept': 'application/json'\n        },\n        body: JSON.stringify({\n          query: 'query cart { cart { id product { id name price image } quantity } }'\n        })\n      });\n      addResult(`Response status: ${response.status}`, response.ok ? 'success' : 'error');\n      const data = await response.json();\n      addResult(`Response data: ${JSON.stringify(data)}`, 'info');\n      if (data.data && data.data.cart) {\n        addResult(`Cart items found: ${data.data.cart.length}`, 'success');\n        if (data.data.cart.length > 0) {\n          // Test remove mutation\n          const firstItemId = data.data.cart[0].id;\n          addResult(`Testing remove mutation for item ${firstItemId}...`, 'info');\n          const removeResponse = await fetch('http://localhost:8000', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Accept': 'application/json'\n            },\n            body: JSON.stringify({\n              query: 'mutation RemoveFromCart($itemId: ID!) { removeFromCart(itemId: $itemId) { id product { id name price image } quantity } }',\n              variables: {\n                itemId: String(firstItemId)\n              }\n            })\n          });\n          const removeData = await removeResponse.json();\n          addResult(`Remove response: ${JSON.stringify(removeData)}`, removeResponse.ok ? 'success' : 'error');\n        }\n      } else if (data.errors) {\n        addResult(`GraphQL errors: ${JSON.stringify(data.errors)}`, 'error');\n      }\n    } catch (error) {\n      addResult(`Network error: ${error.message}`, 'error');\n      console.error('Network test error:', error);\n    }\n    setIsLoading(false);\n  };\n  const clearResults = () => {\n    setTestResults([]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Network Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: testDirectFetch,\n        disabled: isLoading,\n        style: {\n          padding: '10px 20px',\n          backgroundColor: '#4CAF50',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          marginRight: '10px'\n        },\n        children: isLoading ? 'Testing...' : 'Test Direct Fetch'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: clearResults,\n        style: {\n          padding: '10px 20px',\n          backgroundColor: '#888',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px'\n        },\n        children: \"Clear Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Test Results:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxHeight: '400px',\n          overflowY: 'auto',\n          border: '1px solid #ccc',\n          padding: '10px',\n          backgroundColor: '#f9f9f9'\n        },\n        children: [testResults.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: result.type === 'error' ? 'red' : result.type === 'success' ? 'green' : result.type === 'warning' ? 'orange' : 'blue',\n            marginBottom: '5px',\n            fontFamily: 'monospace',\n            fontSize: '12px'\n          },\n          children: [\"[\", result.timestamp, \"] \", result.message]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)), testResults.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#888',\n            fontStyle: 'italic'\n          },\n          children: \"No test results yet. Click \\\"Test Direct Fetch\\\" to start.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n}\n_s(NetworkTest, \"eShieB6+OwvyCDAfByPLs1yVIIw=\");\n_c = NetworkTest;\nexport default NetworkTest;\nvar _c;\n$RefreshReg$(_c, \"NetworkTest\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "NetworkTest", "_s", "testResults", "setTestResults", "isLoading", "setIsLoading", "addResult", "message", "type", "prev", "timestamp", "Date", "toLocaleTimeString", "testDirectFetch", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "query", "status", "ok", "data", "json", "cart", "length", "firstItemId", "id", "removeResponse", "variables", "itemId", "String", "removeData", "errors", "error", "console", "clearResults", "style", "padding", "fontFamily", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "onClick", "disabled", "backgroundColor", "color", "border", "borderRadius", "marginRight", "maxHeight", "overflowY", "map", "result", "index", "fontSize", "fontStyle", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/NetworkTest.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nfunction NetworkTest() {\n  const [testResults, setTestResults] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const addResult = (message, type = 'info') => {\n    setTestResults(prev => [...prev, { \n      message, \n      type, \n      timestamp: new Date().toLocaleTimeString() \n    }]);\n  };\n\n  const testDirectFetch = async () => {\n    setIsLoading(true);\n    addResult('Testing direct fetch to GraphQL endpoint...', 'info');\n\n    try {\n      const response = await fetch('https://localhost:8000', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Accept': 'application/json'\n        },\n        body: JSON.stringify({\n          query: 'query cart { cart { id product { id name price image } quantity } }'\n        })\n      });\n\n      addResult(`Response status: ${response.status}`, response.ok ? 'success' : 'error');\n      \n      const data = await response.json();\n      addResult(`Response data: ${JSON.stringify(data)}`, 'info');\n\n      if (data.data && data.data.cart) {\n        addResult(`Cart items found: ${data.data.cart.length}`, 'success');\n        \n        if (data.data.cart.length > 0) {\n          // Test remove mutation\n          const firstItemId = data.data.cart[0].id;\n          addResult(`Testing remove mutation for item ${firstItemId}...`, 'info');\n          \n          const removeResponse = await fetch('http://localhost:8000', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Accept': 'application/json'\n            },\n            body: JSON.stringify({\n              query: 'mutation RemoveFromCart($itemId: ID!) { removeFromCart(itemId: $itemId) { id product { id name price image } quantity } }',\n              variables: { itemId: String(firstItemId) }\n            })\n          });\n\n          const removeData = await removeResponse.json();\n          addResult(`Remove response: ${JSON.stringify(removeData)}`, removeResponse.ok ? 'success' : 'error');\n        }\n      } else if (data.errors) {\n        addResult(`GraphQL errors: ${JSON.stringify(data.errors)}`, 'error');\n      }\n\n    } catch (error) {\n      addResult(`Network error: ${error.message}`, 'error');\n      console.error('Network test error:', error);\n    }\n\n    setIsLoading(false);\n  };\n\n  const clearResults = () => {\n    setTestResults([]);\n  };\n\n  return (\n    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>\n      <h2>Network Test</h2>\n      \n      <div style={{ marginBottom: '20px' }}>\n        <button \n          onClick={testDirectFetch}\n          disabled={isLoading}\n          style={{ \n            padding: '10px 20px', \n            backgroundColor: '#4CAF50', \n            color: 'white', \n            border: 'none', \n            borderRadius: '4px',\n            marginRight: '10px'\n          }}\n        >\n          {isLoading ? 'Testing...' : 'Test Direct Fetch'}\n        </button>\n        \n        <button \n          onClick={clearResults}\n          style={{ \n            padding: '10px 20px', \n            backgroundColor: '#888', \n            color: 'white', \n            border: 'none', \n            borderRadius: '4px'\n          }}\n        >\n          Clear Results\n        </button>\n      </div>\n\n      <div>\n        <h3>Test Results:</h3>\n        <div style={{ \n          maxHeight: '400px', \n          overflowY: 'auto', \n          border: '1px solid #ccc', \n          padding: '10px',\n          backgroundColor: '#f9f9f9'\n        }}>\n          {testResults.map((result, index) => (\n            <div \n              key={index} \n              style={{ \n                color: result.type === 'error' ? 'red' : \n                       result.type === 'success' ? 'green' : \n                       result.type === 'warning' ? 'orange' : 'blue',\n                marginBottom: '5px',\n                fontFamily: 'monospace',\n                fontSize: '12px'\n              }}\n            >\n              [{result.timestamp}] {result.message}\n            </div>\n          ))}\n          {testResults.length === 0 && (\n            <div style={{ color: '#888', fontStyle: 'italic' }}>\n              No test results yet. Click \"Test Direct Fetch\" to start.\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default NetworkTest;\n\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACO,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMS,SAAS,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,MAAM,KAAK;IAC5CL,cAAc,CAACM,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAC/BF,OAAO;MACPC,IAAI;MACJE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCR,YAAY,CAAC,IAAI,CAAC;IAClBC,SAAS,CAAC,6CAA6C,EAAE,MAAM,CAAC;IAEhE,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,wBAAwB,EAAE;QACrDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,QAAQ,EAAE;QACZ,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC;MAEFf,SAAS,CAAC,oBAAoBQ,QAAQ,CAACQ,MAAM,EAAE,EAAER,QAAQ,CAACS,EAAE,GAAG,SAAS,GAAG,OAAO,CAAC;MAEnF,MAAMC,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCnB,SAAS,CAAC,kBAAkBa,IAAI,CAACC,SAAS,CAACI,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC;MAE3D,IAAIA,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACA,IAAI,CAACE,IAAI,EAAE;QAC/BpB,SAAS,CAAC,qBAAqBkB,IAAI,CAACA,IAAI,CAACE,IAAI,CAACC,MAAM,EAAE,EAAE,SAAS,CAAC;QAElE,IAAIH,IAAI,CAACA,IAAI,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;UAC7B;UACA,MAAMC,WAAW,GAAGJ,IAAI,CAACA,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,CAACG,EAAE;UACxCvB,SAAS,CAAC,oCAAoCsB,WAAW,KAAK,EAAE,MAAM,CAAC;UAEvE,MAAME,cAAc,GAAG,MAAMf,KAAK,CAAC,uBAAuB,EAAE;YAC1DC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,QAAQ,EAAE;YACZ,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBC,KAAK,EAAE,2HAA2H;cAClIU,SAAS,EAAE;gBAAEC,MAAM,EAAEC,MAAM,CAACL,WAAW;cAAE;YAC3C,CAAC;UACH,CAAC,CAAC;UAEF,MAAMM,UAAU,GAAG,MAAMJ,cAAc,CAACL,IAAI,CAAC,CAAC;UAC9CnB,SAAS,CAAC,oBAAoBa,IAAI,CAACC,SAAS,CAACc,UAAU,CAAC,EAAE,EAAEJ,cAAc,CAACP,EAAE,GAAG,SAAS,GAAG,OAAO,CAAC;QACtG;MACF,CAAC,MAAM,IAAIC,IAAI,CAACW,MAAM,EAAE;QACtB7B,SAAS,CAAC,mBAAmBa,IAAI,CAACC,SAAS,CAACI,IAAI,CAACW,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC;MACtE;IAEF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd9B,SAAS,CAAC,kBAAkB8B,KAAK,CAAC7B,OAAO,EAAE,EAAE,OAAO,CAAC;MACrD8B,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC7C;IAEA/B,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMiC,YAAY,GAAGA,CAAA,KAAM;IACzBnC,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,oBACEJ,OAAA;IAAKwC,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAC,QAAA,gBAC/D3C,OAAA;MAAA2C,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAErB/C,OAAA;MAAKwC,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBACnC3C,OAAA;QACEiD,OAAO,EAAEnC,eAAgB;QACzBoC,QAAQ,EAAE7C,SAAU;QACpBmC,KAAK,EAAE;UACLC,OAAO,EAAE,WAAW;UACpBU,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,WAAW,EAAE;QACf,CAAE;QAAAZ,QAAA,EAEDtC,SAAS,GAAG,YAAY,GAAG;MAAmB;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eAET/C,OAAA;QACEiD,OAAO,EAAEV,YAAa;QACtBC,KAAK,EAAE;UACLC,OAAO,EAAE,WAAW;UACpBU,eAAe,EAAE,MAAM;UACvBC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE;QAChB,CAAE;QAAAX,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN/C,OAAA;MAAA2C,QAAA,gBACE3C,OAAA;QAAA2C,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB/C,OAAA;QAAKwC,KAAK,EAAE;UACVgB,SAAS,EAAE,OAAO;UAClBC,SAAS,EAAE,MAAM;UACjBJ,MAAM,EAAE,gBAAgB;UACxBZ,OAAO,EAAE,MAAM;UACfU,eAAe,EAAE;QACnB,CAAE;QAAAR,QAAA,GACCxC,WAAW,CAACuD,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC7B5D,OAAA;UAEEwC,KAAK,EAAE;YACLY,KAAK,EAAEO,MAAM,CAAClD,IAAI,KAAK,OAAO,GAAG,KAAK,GAC/BkD,MAAM,CAAClD,IAAI,KAAK,SAAS,GAAG,OAAO,GACnCkD,MAAM,CAAClD,IAAI,KAAK,SAAS,GAAG,QAAQ,GAAG,MAAM;YACpDuC,YAAY,EAAE,KAAK;YACnBN,UAAU,EAAE,WAAW;YACvBmB,QAAQ,EAAE;UACZ,CAAE;UAAAlB,QAAA,GACH,GACE,EAACgB,MAAM,CAAChD,SAAS,EAAC,IAAE,EAACgD,MAAM,CAACnD,OAAO;QAAA,GAV/BoD,KAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWP,CACN,CAAC,EACD5C,WAAW,CAACyB,MAAM,KAAK,CAAC,iBACvB5B,OAAA;UAAKwC,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEU,SAAS,EAAE;UAAS,CAAE;UAAAnB,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC7C,EAAA,CA3IQD,WAAW;AAAA8D,EAAA,GAAX9D,WAAW;AA6IpB,eAAeA,WAAW;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}