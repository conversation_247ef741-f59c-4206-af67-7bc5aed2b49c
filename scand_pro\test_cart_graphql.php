<?php
require_once 'config/database.php';
require_once 'graphql/GraphQLResolver.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "Testing cart GraphQL query...\n\n";
    
    $resolver = new GraphQLResolver($conn);
    $cartData = $resolver->getCart();
    
    echo "Cart data with attributes:\n";
    echo json_encode($cartData, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
