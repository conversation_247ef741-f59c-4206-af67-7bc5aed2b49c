# Production Deployment Checklist

## Pre-Deployment Preparation

### ✅ Frontend (React App)
- [ ] Run `npm run build` to create production build
- [ ] Test the build locally: `npx serve -s build`
- [ ] Update API endpoints to production URLs
- [ ] Remove console.log statements
- [ ] Optimize images and assets
- [ ] Test all functionality in build version

### ✅ Backend (PHP GraphQL API)
- [ ] Update database configuration for production
- [ ] Remove or secure test files
- [ ] Set up proper error logging
- [ ] Configure CORS for production domain
- [ ] Test GraphQL endpoints
- [ ] Ensure all dependencies are installed

### ✅ Database
- [ ] Export current database: `mysqldump -u root -p scand_test > database_backup.sql`
- [ ] Clean up test data if needed
- [ ] Verify all tables and relationships
- [ ] Create production database backup strategy

## Hosting Setup

### ✅ Shared Hosting (Recommended)
- [ ] Purchase hosting plan with PHP 8.x and MySQL support
- [ ] Set up domain name (or use provided subdomain)
- [ ] Access cPanel or hosting control panel
- [ ] Create MySQL database and user
- [ ] Note down database credentials

### ✅ File Upload
- [ ] Upload React build files to `public_html/` (root)
- [ ] Upload PHP backend to `public_html/api/` or `public_html/backend/`
- [ ] Upload `.htaccess` files to appropriate directories
- [ ] Set correct file permissions (755 for folders, 644 for files)

### ✅ Database Import
- [ ] Access phpMyAdmin from cPanel
- [ ] Select your production database
- [ ] Import `database_dump.sql`
- [ ] Verify all tables imported correctly
- [ ] Test database connection

## Configuration Updates

### ✅ Backend Configuration
- [ ] Update `config/database.php` with production credentials
- [ ] Update CORS headers with production domain
- [ ] Set up error logging
- [ ] Configure PHP settings if needed

### ✅ Frontend Configuration
- [ ] Update Apollo Client with production API URL
- [ ] Test API connectivity
- [ ] Verify all GraphQL queries work
- [ ] Check browser console for errors

## Testing & Verification

### ✅ Functionality Testing
- [ ] Homepage loads correctly
- [ ] Product listing works
- [ ] Product details page works
- [ ] Add to cart functionality
- [ ] Cart operations (add, remove, update)
- [ ] Order placement
- [ ] Category filtering
- [ ] Search functionality (if implemented)

### ✅ Performance Testing
- [ ] Page load speeds acceptable
- [ ] Images load properly
- [ ] API response times reasonable
- [ ] Mobile responsiveness
- [ ] Cross-browser compatibility

### ✅ Security Verification
- [ ] HTTPS enabled (SSL certificate)
- [ ] Database credentials secure
- [ ] No sensitive information exposed
- [ ] CORS properly configured
- [ ] File permissions correct

## Post-Deployment

### ✅ Monitoring Setup
- [ ] Set up Google Analytics (optional)
- [ ] Monitor error logs
- [ ] Set up uptime monitoring
- [ ] Create backup schedule

### ✅ SEO & Performance
- [ ] Add meta tags for SEO
- [ ] Create sitemap.xml
- [ ] Optimize for search engines
- [ ] Test page speed (Google PageSpeed Insights)

## Troubleshooting Common Issues

### Database Connection Issues
```bash
# Check if database credentials are correct
# Verify database user has proper permissions
# Ensure MySQL service is running on hosting server
```

### CORS Errors
```php
// Add to your GraphQL endpoint
header("Access-Control-Allow-Origin: https://yourdomain.com");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
```

### React Router Issues
```apache
# Ensure .htaccess has proper rewrite rules
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

### File Permission Issues
```bash
# Set correct permissions
find /path/to/your/files -type d -exec chmod 755 {} \;
find /path/to/your/files -type f -exec chmod 644 {} \;
```

## Emergency Rollback Plan

### ✅ Backup Strategy
- [ ] Keep local copy of working version
- [ ] Database backup before deployment
- [ ] File backup before upload
- [ ] Document rollback procedure

### ✅ Quick Rollback Steps
1. Restore previous file version
2. Restore database backup if needed
3. Update configuration files
4. Test functionality
5. Monitor for issues

## Success Metrics

### ✅ Deployment Successful When:
- [ ] Website loads at your domain
- [ ] All pages accessible
- [ ] Database operations work
- [ ] No console errors
- [ ] Mobile version works
- [ ] SSL certificate active
- [ ] Performance acceptable

## Contact Information for Support

### Hosting Provider Support
- [ ] Save hosting provider contact info
- [ ] Note support ticket system
- [ ] Document any special configurations

### Development Team
- [ ] Document who can help with issues
- [ ] Save important passwords securely
- [ ] Create maintenance schedule

---

**Remember**: Always test thoroughly before going live, and keep backups of everything!
