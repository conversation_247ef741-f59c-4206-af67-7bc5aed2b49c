<?php
echo "Testing exact frontend GraphQL queries...\n\n";

// Test the exact cart query that the frontend sends
$cartQuery = [
    'query' => 'query cart { cart { id product { id name price image } quantity } }'
];

echo "1. Testing cart query...\n";
echo "Query: " . $cartQuery['query'] . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($cartQuery));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}

echo "Response: $response\n\n";

if ($response) {
    $data = json_decode($response, true);
    if (isset($data['data']['cart']) && count($data['data']['cart']) > 0) {
        echo "✅ Cart query successful!\n";
        echo "Cart items found: " . count($data['data']['cart']) . "\n";
        
        // Test the exact remove mutation that the frontend sends
        $firstItemId = $data['data']['cart'][0]['id'];
        echo "\n2. Testing remove mutation...\n";
        
        $removeMutation = [
            'query' => 'mutation RemoveFromCart($itemId: ID!) { removeFromCart(itemId: $itemId) { id } }',
            'variables' => [
                'itemId' => (string)$firstItemId
            ]
        ];
        
        echo "Query: " . $removeMutation['query'] . "\n";
        echo "Variables: " . json_encode($removeMutation['variables']) . "\n";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($removeMutation));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $removeResponse = curl_exec($ch);
        $removeHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $removeError = curl_error($ch);
        curl_close($ch);
        
        echo "Remove HTTP Code: $removeHttpCode\n";
        if ($removeError) {
            echo "Remove cURL Error: $removeError\n";
        }
        echo "Remove Response: $removeResponse\n";
        
        if ($removeResponse) {
            $removeData = json_decode($removeResponse, true);
            if (isset($removeData['data']['removeFromCart'])) {
                echo "✅ Remove mutation successful!\n";
                
                // Test cart query again to verify removal
                echo "\n3. Testing cart query after removal...\n";
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000');
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($cartQuery));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Accept: application/json'
                ]);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                
                $finalResponse = curl_exec($ch);
                curl_close($ch);
                
                echo "Final cart response: $finalResponse\n";
                
                $finalData = json_decode($finalResponse, true);
                if (isset($finalData['data']['cart'])) {
                    echo "✅ Final cart items: " . count($finalData['data']['cart']) . "\n";
                    if (count($finalData['data']['cart']) < count($data['data']['cart'])) {
                        echo "✅ Item successfully removed!\n";
                    } else {
                        echo "❌ Item was not removed\n";
                    }
                }
                
            } else {
                echo "❌ Remove mutation failed\n";
                if (isset($removeData['errors'])) {
                    echo "GraphQL Errors: " . json_encode($removeData['errors'], JSON_PRETTY_PRINT) . "\n";
                }
            }
        }
    } else {
        echo "ℹ️  No cart items found\n";
        if (isset($data['errors'])) {
            echo "GraphQL Errors: " . json_encode($data['errors'], JSON_PRETTY_PRINT) . "\n";
        }
    }
} else {
    echo "❌ No response received\n";
}
?>
