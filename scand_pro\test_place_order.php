<?php
require_once 'config/database.php';
require_once 'graphql/GraphQLResolver.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "Testing place order functionality...\n\n";
    
    // Check cart before order
    $stmt = $conn->query('SELECT COUNT(*) as count FROM cart');
    $beforeCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Cart items before order: $beforeCount\n";
    
    if ($beforeCount == 0) {
        echo "Adding test item to cart first...\n";
        $resolver = new GraphQLResolver($conn);
        $resolver->addToCart(['productId' => 'ps-5', 'quantity' => 1]);
        
        $stmt = $conn->query('SELECT COUNT(*) as count FROM cart');
        $beforeCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Cart items after adding test item: $beforeCount\n";
    }
    
    // Place order
    echo "\nPlacing order...\n";
    $resolver = new GraphQLResolver($conn);
    $result = $resolver->placeOrder();
    
    echo "Place order result: " . json_encode($result) . "\n";
    
    // Check cart after order
    $stmt = $conn->query('SELECT COUNT(*) as count FROM cart');
    $afterCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Cart items after order: $afterCount\n";
    
    if ($afterCount == 0) {
        echo "✅ SUCCESS: Cart properly cleared!\n";
    } else {
        echo "❌ FAILED: Cart still has items!\n";
    }
    
    // Test GraphQL cart query
    echo "\nTesting GraphQL cart query...\n";
    $cartData = $resolver->getCart();
    echo "Cart data from GraphQL: " . json_encode($cartData) . "\n";
    echo "Cart items count from GraphQL: " . count($cartData) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
