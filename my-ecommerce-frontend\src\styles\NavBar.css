/* src/components/Navbar.css */

.navbar {
    position: sticky;
    top: 0;
    width: 96%;
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    z-index: 1000;
    height: 50px;
    font-family: Raleway;
  }
  
  .navbar-left, .navbar-right {
    display: flex;
    gap: 1rem;

    a {
      color: black;
      text-decoration: none;
      height: 35px;
    }
    .active {
      color: #5ECE7B;
      border-bottom: 2px solid #5ECE7B;
    }
  }

  
  .nav-link {
    color: white;
    text-decoration: none;
    font-size: 1rem;
    transition: color 0.3s ease;
  }
  
  .nav-link:hover {
    color: #ddd;
  }

  .cart-icon-container {
    position: relative;
    cursor: pointer;
  }
  
  .cart-icon {
    font-size: 1.5rem;
  }
  
  .cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: red;
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 50%;
    text-align: center;
  }
  
  