<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/config/database.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "Testing database connection...\n";
echo "Host: " . ($_ENV['DB_HOST'] ?? 'localhost') . "\n";
echo "Database: " . ($_ENV['DB_NAME'] ?? 'scand_test') . "\n";
echo "User: " . ($_ENV['DB_USER'] ?? 'root') . "\n";
echo "Password: " . (empty($_ENV['DB_PASS']) ? '(empty)' : '(set)') . "\n\n";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "✅ Database connection successful!\n\n";
        
        // Test query to count products
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM products");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Products in database: " . $result['count'] . "\n";
        
        // Test query to count categories
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM categories");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Categories in database: " . $result['count'] . "\n";
        
        // List all tables
        $stmt = $conn->prepare("SHOW TABLES");
        $stmt->execute();
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "\nTables in database:\n";
        foreach ($tables as $table) {
            echo "- " . $table . "\n";
        }
        
    } else {
        echo "❌ Failed to connect to database\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
