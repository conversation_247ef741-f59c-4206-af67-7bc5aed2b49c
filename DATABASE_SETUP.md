# SCAND E-Commerce Database Setup

## Database Information
- **Database Name**: `scand_test`
- **Host**: localhost
- **User**: root
- **Password**: (empty - XAMPP default)

## Tables Created
- `products` - Product information
- `categories` - Product categories
- `attributes` - Product attributes (size, color, etc.)
- `attribute_items` - Attribute values
- `prices` - Product pricing
- `product_gallery` - Product images
- `cart` - Shopping cart items
- `users` - User accounts
- `orders` - Order information

## Sample Data
- **8 Products** including Apple devices, Nike shoes, gaming consoles
- **3 Categories**: all, clothes, tech
- **Product attributes** like size, color, capacity
- **Pricing information** in USD
- **Product images** with external URLs

## Configuration Files
- `scand_pro/.env` - Backend environment variables
- `my-ecommerce-frontend/.env` - Frontend environment variables
- `scand_pro/config/database.php` - Database connection class

## How to Start the Application

### 1. Start the Backend (GraphQL API)
```bash
cd scand_pro
php -S localhost:8000
```

### 2. Start the Frontend (React App)
```bash
cd my-ecommerce-frontend
npm start
```

## Testing the Setup
Run the test script to verify everything is working:
```bash
php test_full_setup.php
```

## Database Connection Test
Run the database connection test:
```bash
cd scand_pro
php test_connection.php
```

## GraphQL Endpoint
- **URL**: http://localhost:8000
- **Method**: POST
- **Content-Type**: application/json

### Sample Request
```json
{
  "query": "query",
  "operationName": "products"
}
```

## Available Operations
- `products` - Get all products
- `product` - Get single product by ID
- `attributes` - Get product attributes
- `AddToCart` - Add item to cart
- `getCart` - Get cart items
- `updateCart` - Update cart item quantity

## Prerequisites
- XAMPP with MySQL/MariaDB running
- PHP 8.x
- Node.js and npm
- Composer (for PHP dependencies)

## Troubleshooting
1. **Database connection fails**: Make sure XAMPP MySQL service is running
2. **GraphQL endpoint not responding**: Ensure PHP server is running on port 8000
3. **Frontend can't connect**: Check that .env file points to http://localhost:8000
