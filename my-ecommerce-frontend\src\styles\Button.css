/* Default button styling */
.custom-button {
    display: inline-block;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    font-weight: bold;
    color: white;
    background-color: 218838;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    transition: background-color 0.3s ease, transform 0.2s ease;
    margin: 20px;
  }
  
  /* Hover effect */
  .custom-button:hover {
    background-color: #0056b3;
    transform: scale(1.05);
  }
  
  /* Disabled state */
  .custom-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    transform: none;
  }
  
  /* Add specific styles for different types of buttons */
  .custom-button.primary {
    background-color: #007bff;
  }
  
  .custom-button.secondary {
    background-color: #6c757d;
  }
  
  .custom-button.success {
    background-color: #28a745;
  }
  
  .custom-button.danger {
    background-color: #dc3545;
  }
  
  .custom-button.warning {
    background-color: #ffc107;
  }
  
  .custom-button.info {
    background-color: #17a2b8;
  }
  