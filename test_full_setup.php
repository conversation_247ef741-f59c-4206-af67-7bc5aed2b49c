<?php
echo "=== SCAND E-COMMERCE SETUP TEST ===\n\n";

// Test 1: Database Connection
echo "1. Testing Database Connection...\n";
require_once 'scand_pro/vendor/autoload.php';
require_once 'scand_pro/config/database.php';

use Dotenv\Dotenv;

$dotenv = Dotenv::createImmutable(__DIR__ . '/scand_pro');
$dotenv->load();

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "   ✅ Database connection successful\n";
        
        // Count products
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM products");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "   📦 Products: " . $result['count'] . "\n";
        
        // Count categories
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM categories");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "   📂 Categories: " . $result['count'] . "\n";
    } else {
        echo "   ❌ Database connection failed\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "   ❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n2. Testing GraphQL Endpoint...\n";

// Test if PHP server is running on port 8000
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode([
            'query' => 'query',
            'operationName' => 'products'
        ]),
        'timeout' => 5
    ]
]);

$result = @file_get_contents('http://localhost:8000', false, $context);

if ($result !== false) {
    $data = json_decode($result, true);
    if (isset($data['data']) && is_array($data['data'])) {
        echo "   ✅ GraphQL endpoint responding\n";
        echo "   📊 Returned " . count($data['data']) . " products\n";
    } else {
        echo "   ⚠️  GraphQL endpoint responding but with unexpected format\n";
        echo "   Response: " . substr($result, 0, 100) . "...\n";
    }
} else {
    echo "   ❌ GraphQL endpoint not accessible\n";
    echo "   Make sure PHP server is running: php -S localhost:8000\n";
}

echo "\n3. Configuration Summary...\n";
echo "   🗄️  Database: " . ($_ENV['DB_NAME'] ?? 'scand_test') . " on " . ($_ENV['DB_HOST'] ?? 'localhost') . "\n";
echo "   🔧 Backend: http://localhost:8000\n";
echo "   🌐 Frontend: Should be configured to use http://localhost:8000\n";

echo "\n=== SETUP COMPLETE ===\n";
echo "To start the application:\n";
echo "1. Backend: cd scand_pro && php -S localhost:8000\n";
echo "2. Frontend: cd my-ecommerce-frontend && npm start\n";
?>
