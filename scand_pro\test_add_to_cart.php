<?php
require_once 'vendor/autoload.php';
require_once 'config/database.php';
require_once 'graphql/GraphQLResolver.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "Testing addToCart functionality...\n\n";

try {
    // Connect to database
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    echo "✅ Database connected successfully\n";
    
    // Clear cart first
    $stmt = $conn->prepare("DELETE FROM cart");
    $stmt->execute();
    echo "🧹 Cart cleared\n";
    
    // Test addToCart resolver
    $resolver = new GraphQLResolver($conn);
    $result = $resolver->addToCart(['productId' => 'ps-5', 'quantity' => 1]);
    
    echo "✅ AddToCart successful!\n";
    echo "Result: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
    
    // Test via HTTP GraphQL
    echo "\n🌐 Testing via HTTP GraphQL...\n";
    
    $addMutation = [
        'query' => 'mutation AddToCart($productId: ID!, $quantity: Int!) { addToCart(productId: $productId, quantity: $quantity) { id product { id name price image } quantity } }',
        'variables' => [
            'productId' => 'apple-airpods-pro',
            'quantity' => 2
        ]
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($addMutation));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n";
    
    if ($response) {
        $data = json_decode($response, true);
        if (isset($data['data']['addToCart'])) {
            echo "✅ HTTP AddToCart successful!\n";
        } else {
            echo "❌ HTTP AddToCart failed\n";
            if (isset($data['errors'])) {
                echo "Errors: " . json_encode($data['errors'], JSON_PRETTY_PRINT) . "\n";
            }
        }
    }
    
    // Check final cart state
    echo "\n📦 Final cart state:\n";
    $cartData = $resolver->getCart();
    echo json_encode($cartData, JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
