<?php
require_once 'vendor/autoload.php';
require_once 'config/database.php';
require_once 'graphql/GraphQLResolver.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "Testing GraphQL cart query...\n";

try {
    // Connect to database
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    echo "✅ Database connected successfully\n";
    
    // Test getCart resolver
    $resolver = new GraphQLResolver($conn);
    $cartData = $resolver->getCart();
    
    echo "✅ Cart query successful!\n";
    echo "Cart data: " . json_encode($cartData, JSON_PRETTY_PRINT) . "\n";
    
    if (count($cartData) > 0) {
        echo "\n🧪 Testing removeFromCart with first item...\n";
        $firstItem = $cartData[0];
        echo "Removing item ID: " . $firstItem['id'] . "\n";
        
        $removeResult = $resolver->removeFromCart(['itemId' => $firstItem['id']]);
        echo "✅ Remove successful!\n";
        echo "Remove result: " . json_encode($removeResult, JSON_PRETTY_PRINT) . "\n";
        
        // Check cart again
        $cartDataAfter = $resolver->getCart();
        echo "\n📦 Cart after removal: " . json_encode($cartDataAfter, JSON_PRETTY_PRINT) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
