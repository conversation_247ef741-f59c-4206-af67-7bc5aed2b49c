<?php
require_once 'vendor/autoload.php';
require_once 'config/database.php';
require_once 'graphql/GraphQLResolver.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "Testing removeFromCart functionality...\n\n";

try {
    // Connect to database
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    echo "✅ Database connected successfully\n";
    
    // Check current cart items
    $stmt = $conn->query('SELECT * FROM cart');
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "📦 Current cart items: " . count($items) . "\n";
    
    if (count($items) > 0) {
        foreach($items as $item) {
            echo "   - ID: {$item['id']}, Product: {$item['product_id']}, Quantity: {$item['quantity']}\n";
        }
        
        // Test removeFromCart with the first item
        $firstItemId = $items[0]['id'];
        echo "\n🧪 Testing removeFromCart with item ID: $firstItemId\n";
        
        $resolver = new GraphQLResolver($conn);
        $result = $resolver->removeFromCart(['itemId' => $firstItemId]);
        
        echo "✅ Remove operation successful!\n";
        echo "Result: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
        
        // Check cart items after removal
        $stmt = $conn->query('SELECT * FROM cart');
        $itemsAfter = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "\n📦 Cart items after removal: " . count($itemsAfter) . "\n";
        
        if (count($itemsAfter) < count($items)) {
            echo "✅ Item successfully removed from cart!\n";
        } else {
            echo "❌ Item was not removed from cart\n";
        }
        
    } else {
        echo "ℹ️  No items in cart to test removal\n";
        
        // Add a test item first
        echo "➕ Adding a test item to cart...\n";
        $resolver = new GraphQLResolver($conn);
        $addResult = $resolver->addToCart(['productId' => 'ps-5', 'quantity' => 1]);
        echo "✅ Test item added: " . json_encode($addResult, JSON_PRETTY_PRINT) . "\n";
        
        // Now test removal
        $stmt = $conn->query('SELECT * FROM cart ORDER BY id DESC LIMIT 1');
        $newItem = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($newItem) {
            echo "\n🧪 Testing removeFromCart with new item ID: {$newItem['id']}\n";
            $result = $resolver->removeFromCart(['itemId' => $newItem['id']]);
            echo "✅ Remove operation successful!\n";
            echo "Result: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
