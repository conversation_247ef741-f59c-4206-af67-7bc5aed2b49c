.products {
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	width: 90%;
	max-width: 1200px;
	margin: 0 auto;
  background: #fff;
  font-family: sans-serif;
}


.product-card {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 20rem;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 2px 2px 5px 0px rgba(0, 64, 128, 0.1);
}

.product-card:hover {
  transform: scale(1.05); 
  box-shadow: 4px 4px 10px 0px rgba(0, 64, 128, 0.3); 
  border: 2px solid rgba(0, 64, 128, 0.2);
  background-color: #f9f9f9;

  
  
}

.product-card__image {
  max-width: 100%;
  height: auto;
}

.product-card__description,
.product-card__price,
.product-card__brand {
  margin: 0.2rem 0;
  font-size: 1rem;
}

.product-card__brand {
  font-weight: bold;
  text-transform: uppercase;
}

.product-card__description {
  font-weight: normal;
}

.product-card__price {
  font-weight: bold;
}

.product-card__btn-wishlist {
  position: absolute;
  top: 10px;
  right: 10px;
  border-radius: 50%;
  height: 40px;
  width: 40px;
  border: none;
  background-color: white;
  padding: 12px 10px 10px;
  box-shadow: 2px 2px 5px 0px rgba(0, 64, 128, 0.1);
}

.product-card__btn-wishlist svg {
  fill: lightgrey;
}

.product-link{
  text-decoration: none;
  color: grey;
}

.header {
  display: block;
  width: 100%;
  margin-bottom: 50px;
  color: black;
  font-family: Raleway;
}

.price {
  font-weight: bold;
}

/* Add to Cart Button */
.add-to-cart {
  display: block;
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: bold;
  color: white;
  background-color: turquoise;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
  margin: 0;
  position: relative;
  top: -10px;
}

.add-to-cart:hover {
  background-color: rgb(25, 139, 128);
}