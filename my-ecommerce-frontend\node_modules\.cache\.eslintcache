[{"D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\index.js": "1", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\apolloClient.js": "2", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\App.js": "3", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\CartPopup.js": "4", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductDetails.js": "5", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Navbar.js": "6", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductList.js": "7", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Button.js": "8", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\mutations.js": "9", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\queries.js": "10", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\TestCart.js": "11", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\NetworkTest.js": "12", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\CartTest.js": "13", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\PlaceOrderTest.js": "14", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\DirectPlaceOrderTest.js": "15", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\FinalCartTest.js": "16", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\DirectPlaceOrderEndpointTest.js": "17", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\SmartRemovalTest.js": "18"}, {"size": 352, "mtime": 1731685001174, "results": "19", "hashOfConfig": "20"}, {"size": 2056, "mtime": 1750181541492, "results": "21", "hashOfConfig": "20"}, {"size": 1907, "mtime": 1748650952625, "results": "22", "hashOfConfig": "20"}, {"size": 15999, "mtime": 1748651669275, "results": "23", "hashOfConfig": "20"}, {"size": 7105, "mtime": 1748651591323, "results": "24", "hashOfConfig": "20"}, {"size": 1874, "mtime": 1748651552516, "results": "25", "hashOfConfig": "20"}, {"size": 1523, "mtime": 1748651565206, "results": "26", "hashOfConfig": "20"}, {"size": 455, "mtime": 1748651531040, "results": "27", "hashOfConfig": "20"}, {"size": 1802, "mtime": 1748617624540, "results": "28", "hashOfConfig": "20"}, {"size": 729, "mtime": 1748646067482, "results": "29", "hashOfConfig": "20"}, {"size": 3976, "mtime": 1748617224133, "results": "30", "hashOfConfig": "20"}, {"size": 4379, "mtime": 1750181523281, "results": "31", "hashOfConfig": "20"}, {"size": 1684, "mtime": 1748646244740, "results": "32", "hashOfConfig": "20"}, {"size": 3658, "mtime": 1748649940777, "results": "33", "hashOfConfig": "20"}, {"size": 4690, "mtime": 1748650207272, "results": "34", "hashOfConfig": "20"}, {"size": 4953, "mtime": 1748650269579, "results": "35", "hashOfConfig": "20"}, {"size": 4979, "mtime": 1748650602541, "results": "36", "hashOfConfig": "20"}, {"size": 6290, "mtime": 1748650933795, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10vzdib", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\index.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\apolloClient.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\App.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\CartPopup.js", ["92", "93", "94"], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductDetails.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Navbar.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductList.js", ["95", "96"], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Button.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\mutations.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\queries.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\TestCart.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\NetworkTest.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\CartTest.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\PlaceOrderTest.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\DirectPlaceOrderTest.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\FinalCartTest.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\DirectPlaceOrderEndpointTest.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\SmartRemovalTest.js", [], [], {"ruleId": "97", "severity": 1, "message": "98", "line": 12, "column": 9, "nodeType": "99", "messageId": "100", "endLine": 12, "endColumn": 15}, {"ruleId": "97", "severity": 1, "message": "101", "line": 40, "column": 10, "nodeType": "99", "messageId": "100", "endLine": 40, "endColumn": 20}, {"ruleId": "97", "severity": 1, "message": "102", "line": 183, "column": 9, "nodeType": "99", "messageId": "100", "endLine": 183, "endColumn": 28}, {"ruleId": "97", "severity": 1, "message": "103", "line": 3, "column": 8, "nodeType": "99", "messageId": "100", "endLine": 3, "endColumn": 14}, {"ruleId": "104", "severity": 1, "message": "105", "line": 34, "column": 13, "nodeType": "106", "endLine": 34, "endColumn": 76}, "no-unused-vars", "'client' is assigned a value but never used.", "Identifier", "unusedVar", "'placeOrder' is assigned a value but never used.", "'handleRemoveProduct' is assigned a value but never used.", "'Button' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement"]