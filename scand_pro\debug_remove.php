<?php
require_once 'config/database.php';
require_once 'graphql/GraphQLResolver.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "Debugging remove from cart functionality...\n\n";
    
    // Check current cart items
    $stmt = $conn->query('SELECT * FROM cart');
    $cartItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Current cart items:\n";
    foreach ($cartItems as $item) {
        echo "ID: {$item['id']}, Product ID: {$item['product_id']}, Quantity: {$item['quantity']}\n";
    }
    
    if (empty($cartItems)) {
        echo "No items in cart to test with.\n";
        exit;
    }
    
    // Test removing the first item
    $testItemId = $cartItems[0]['id'];
    echo "\nTesting removal of item ID: $testItemId\n";
    
    $resolver = new GraphQLResolver();
    $result = $resolver->removeFromCart(['itemId' => $testItemId]);
    
    echo "Remove result: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
    
    // Check cart after removal
    $stmt = $conn->query('SELECT * FROM cart');
    $cartItemsAfter = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nCart items after removal:\n";
    foreach ($cartItemsAfter as $item) {
        echo "ID: {$item['id']}, Product ID: {$item['product_id']}, Quantity: {$item['quantity']}\n";
    }
    
    $beforeCount = count($cartItems);
    $afterCount = count($cartItemsAfter);
    
    echo "\nItems before: $beforeCount\n";
    echo "Items after: $afterCount\n";
    
    if ($afterCount == $beforeCount - 1) {
        echo "✅ SUCCESS: Item removed successfully!\n";
    } else {
        echo "❌ FAILED: Item was not removed\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
