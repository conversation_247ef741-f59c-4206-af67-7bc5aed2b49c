<?php
echo "Testing GraphQL HTTP endpoint...\n";

// Test cart query
$cartQuery = [
    'query' => 'query { cart { id product { id name price image } quantity } }'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($cartQuery));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}

echo "Response: $response\n";

if ($response) {
    $data = json_decode($response, true);
    if (isset($data['data']['cart']) && count($data['data']['cart']) > 0) {
        echo "\n✅ Cart query successful via HTTP!\n";
        echo "Cart items found: " . count($data['data']['cart']) . "\n";
        
        // Test remove mutation
        $firstItemId = $data['data']['cart'][0]['id'];
        echo "\n🧪 Testing remove mutation for item ID: $firstItemId\n";
        
        $removeMutation = [
            'query' => 'mutation { removeFromCart(itemId: "' . $firstItemId . '") { id product { id name price image } quantity } }'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($removeMutation));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $removeResponse = curl_exec($ch);
        $removeHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $removeError = curl_error($ch);
        curl_close($ch);
        
        echo "Remove HTTP Code: $removeHttpCode\n";
        if ($removeError) {
            echo "Remove cURL Error: $removeError\n";
        }
        echo "Remove Response: $removeResponse\n";
        
        if ($removeResponse) {
            $removeData = json_decode($removeResponse, true);
            if (isset($removeData['data']['removeFromCart'])) {
                echo "✅ Remove mutation successful via HTTP!\n";
            } else {
                echo "❌ Remove mutation failed\n";
                if (isset($removeData['errors'])) {
                    echo "GraphQL Errors: " . json_encode($removeData['errors'], JSON_PRETTY_PRINT) . "\n";
                }
            }
        }
    } else {
        echo "❌ No cart items found or query failed\n";
        if (isset($data['errors'])) {
            echo "GraphQL Errors: " . json_encode($data['errors'], JSON_PRETTY_PRINT) . "\n";
        }
    }
} else {
    echo "❌ No response received\n";
}
?>
