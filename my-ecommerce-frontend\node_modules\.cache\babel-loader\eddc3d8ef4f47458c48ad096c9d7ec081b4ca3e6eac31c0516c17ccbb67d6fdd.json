{"ast": null, "code": "import { ApolloClient, InMemoryCache, from, createHttpLink } from '@apollo/client';\nimport { onError } from '@apollo/client/link/error';\n\n// Error handling link\nconst errorLink = onError(({\n  graphQLErrors,\n  networkError,\n  operation,\n  forward\n}) => {\n  console.log('Apollo Client Error Handler triggered');\n  console.log('Operation:', operation.operationName);\n  console.log('Variables:', operation.variables);\n  if (graphQLErrors) {\n    console.error('GraphQL Errors:', graphQLErrors);\n    graphQLErrors.forEach(({\n      message,\n      locations,\n      path\n    }) => console.error(`[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`));\n  }\n  if (networkError) {\n    console.error(`[Network error]:`, networkError);\n    console.error('Network error details:', {\n      name: networkError.name,\n      message: networkError.message,\n      statusCode: networkError.statusCode,\n      result: networkError.result\n    });\n  }\n});\n\n// HTTP link\nconst graphqlEndpoint = process.env.REACT_APP_GRAPHQL_ENDPOINT || 'https://localhost:8000';\nconsole.log('GraphQL Endpoint:', graphqlEndpoint);\nconst httpLink = createHttpLink({\n  uri: graphqlEndpoint\n});\nconst client = new ApolloClient({\n  link: from([errorLink, httpLink]),\n  cache: new InMemoryCache({\n    typePolicies: {\n      Query: {\n        fields: {\n          cart: {\n            merge: false,\n            // Don't merge, replace the entire array\n            // Always fetch from network after mutations\n            read(existing, {\n              canRead\n            }) {\n              console.log('Cart cache read - existing:', existing);\n              return existing;\n            }\n          }\n        }\n      }\n    }\n  }),\n  defaultOptions: {\n    watchQuery: {\n      fetchPolicy: 'network-only',\n      errorPolicy: 'all'\n    },\n    query: {\n      fetchPolicy: 'network-only',\n      errorPolicy: 'all'\n    },\n    mutate: {\n      errorPolicy: 'all'\n    }\n  }\n});\n\n// Clear cache on startup\nclient.clearStore();\nexport default client;", "map": {"version": 3, "names": ["ApolloClient", "InMemoryCache", "from", "createHttpLink", "onError", "errorLink", "graphQLErrors", "networkError", "operation", "forward", "console", "log", "operationName", "variables", "error", "for<PERSON>ach", "message", "locations", "path", "name", "statusCode", "result", "graphqlEndpoint", "process", "env", "REACT_APP_GRAPHQL_ENDPOINT", "httpLink", "uri", "client", "link", "cache", "typePolicies", "Query", "fields", "cart", "merge", "read", "existing", "canRead", "defaultOptions", "watch<PERSON><PERSON>y", "fetchPolicy", "errorPolicy", "query", "mutate", "clearStore"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/apolloClient.js"], "sourcesContent": ["import { ApolloClient, InMemoryCache, from, createHttpLink } from '@apollo/client';\r\nimport { onError } from '@apollo/client/link/error';\r\n\r\n// Error handling link\r\nconst errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {\r\n  console.log('Apollo Client Error Handler triggered');\r\n  console.log('Operation:', operation.operationName);\r\n  console.log('Variables:', operation.variables);\r\n\r\n  if (graphQLErrors) {\r\n    console.error('GraphQL Errors:', graphQLErrors);\r\n    graphQLErrors.forEach(({ message, locations, path }) =>\r\n      console.error(\r\n        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`\r\n      )\r\n    );\r\n  }\r\n  if (networkError) {\r\n    console.error(`[Network error]:`, networkError);\r\n    console.error('Network error details:', {\r\n      name: networkError.name,\r\n      message: networkError.message,\r\n      statusCode: networkError.statusCode,\r\n      result: networkError.result\r\n    });\r\n  }\r\n});\r\n\r\n// HTTP link\r\nconst graphqlEndpoint = process.env.REACT_APP_GRAPHQL_ENDPOINT || 'https://localhost:8000';\r\nconsole.log('GraphQL Endpoint:', graphqlEndpoint);\r\n\r\nconst httpLink = createHttpLink({\r\n  uri: graphqlEndpoint,\r\n});\r\n\r\nconst client = new ApolloClient({\r\n  link: from([errorLink, httpLink]),\r\n  cache: new InMemoryCache({\r\n    typePolicies: {\r\n      Query: {\r\n        fields: {\r\n          cart: {\r\n            merge: false, // Don't merge, replace the entire array\r\n            // Always fetch from network after mutations\r\n            read(existing, { canRead }) {\r\n              console.log('Cart cache read - existing:', existing);\r\n              return existing;\r\n            }\r\n          },\r\n        },\r\n      },\r\n    },\r\n  }),\r\n  defaultOptions: {\r\n    watchQuery: {\r\n      fetchPolicy: 'network-only',\r\n      errorPolicy: 'all',\r\n    },\r\n    query: {\r\n      fetchPolicy: 'network-only',\r\n      errorPolicy: 'all',\r\n    },\r\n    mutate: {\r\n      errorPolicy: 'all',\r\n    },\r\n  },\r\n});\r\n\r\n// Clear cache on startup\r\nclient.clearStore();\r\n\r\nexport default client;\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,EAAEC,IAAI,EAAEC,cAAc,QAAQ,gBAAgB;AAClF,SAASC,OAAO,QAAQ,2BAA2B;;AAEnD;AACA,MAAMC,SAAS,GAAGD,OAAO,CAAC,CAAC;EAAEE,aAAa;EAAEC,YAAY;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EACjFC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;EACpDD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEH,SAAS,CAACI,aAAa,CAAC;EAClDF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEH,SAAS,CAACK,SAAS,CAAC;EAE9C,IAAIP,aAAa,EAAE;IACjBI,OAAO,CAACI,KAAK,CAAC,iBAAiB,EAAER,aAAa,CAAC;IAC/CA,aAAa,CAACS,OAAO,CAAC,CAAC;MAAEC,OAAO;MAAEC,SAAS;MAAEC;IAAK,CAAC,KACjDR,OAAO,CAACI,KAAK,CACX,6BAA6BE,OAAO,eAAeC,SAAS,WAAWC,IAAI,EAC7E,CACF,CAAC;EACH;EACA,IAAIX,YAAY,EAAE;IAChBG,OAAO,CAACI,KAAK,CAAC,kBAAkB,EAAEP,YAAY,CAAC;IAC/CG,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAE;MACtCK,IAAI,EAAEZ,YAAY,CAACY,IAAI;MACvBH,OAAO,EAAET,YAAY,CAACS,OAAO;MAC7BI,UAAU,EAAEb,YAAY,CAACa,UAAU;MACnCC,MAAM,EAAEd,YAAY,CAACc;IACvB,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAI,wBAAwB;AAC1Ff,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEW,eAAe,CAAC;AAEjD,MAAMI,QAAQ,GAAGvB,cAAc,CAAC;EAC9BwB,GAAG,EAAEL;AACP,CAAC,CAAC;AAEF,MAAMM,MAAM,GAAG,IAAI5B,YAAY,CAAC;EAC9B6B,IAAI,EAAE3B,IAAI,CAAC,CAACG,SAAS,EAAEqB,QAAQ,CAAC,CAAC;EACjCI,KAAK,EAAE,IAAI7B,aAAa,CAAC;IACvB8B,YAAY,EAAE;MACZC,KAAK,EAAE;QACLC,MAAM,EAAE;UACNC,IAAI,EAAE;YACJC,KAAK,EAAE,KAAK;YAAE;YACd;YACAC,IAAIA,CAACC,QAAQ,EAAE;cAAEC;YAAQ,CAAC,EAAE;cAC1B5B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0B,QAAQ,CAAC;cACpD,OAAOA,QAAQ;YACjB;UACF;QACF;MACF;IACF;EACF,CAAC,CAAC;EACFE,cAAc,EAAE;IACdC,UAAU,EAAE;MACVC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE;IACf,CAAC;IACDC,KAAK,EAAE;MACLF,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE;IACf,CAAC;IACDE,MAAM,EAAE;MACNF,WAAW,EAAE;IACf;EACF;AACF,CAAC,CAAC;;AAEF;AACAd,MAAM,CAACiB,UAAU,CAAC,CAAC;AAEnB,eAAejB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}