import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

// Production GraphQL endpoint
// Replace 'yourdomain.com' with your actual domain
const httpLink = createHttpLink({
  uri: process.env.NODE_ENV === 'production' 
    ? 'https://yourdomain.com/api/graphql.php'  // Production URL
    : 'http://localhost:8000/graphql.php',      // Development URL
});

// Auth link for future authentication features
const authLink = setContext((_, { headers }) => {
  // Get the authentication token from local storage if it exists
  const token = localStorage.getItem('token');
  
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
      'Content-Type': 'application/json',
    }
  }
});

// Error handling link
import { onError } from '@apollo/client/link/error';

const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
      );
    });
  }

  if (networkError) {
    console.error(`[Network error]: ${networkError}`);
    
    // Handle specific network errors
    if (networkError.statusCode === 404) {
      console.error('GraphQL endpoint not found. Check your API URL.');
    } else if (networkError.statusCode === 500) {
      console.error('Server error. Please try again later.');
    }
  }
});

// Create Apollo Client with error handling and auth
const client = new ApolloClient({
  link: errorLink.concat(authLink.concat(httpLink)),
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          // Cache policy for products
          products: {
            merge(existing = [], incoming) {
              return incoming;
            },
          },
          // Cache policy for cart
          cart: {
            merge(existing = [], incoming) {
              return incoming;
            },
          },
        },
      },
    },
  }),
  // Development vs Production settings
  connectToDevTools: process.env.NODE_ENV === 'development',
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
    },
    query: {
      errorPolicy: 'all',
    },
  },
});

export default client;

/**
 * Production Deployment Instructions:
 * 
 * 1. Update the production URI above with your actual domain
 * 2. Ensure your backend API is accessible at that URL
 * 3. Test the connection in production environment
 * 4. Monitor for CORS issues and update backend headers if needed
 * 
 * Environment Variables (optional):
 * Create a .env file in your React app root:
 * 
 * REACT_APP_API_URL=https://yourdomain.com/api/graphql.php
 * 
 * Then use: process.env.REACT_APP_API_URL
 */
