<?php
require_once 'vendor/autoload.php';
require_once 'config/database.php';
require_once 'graphql/GraphQLResolver.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "Adding test item to cart...\n";

try {
    // Connect to database
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    echo "✅ Database connected successfully\n";
    
    // Add a test item
    $resolver = new GraphQLResolver($conn);
    $result = $resolver->addToCart(['productId' => 'ps-5', 'quantity' => 1]);
    
    echo "✅ Test item added successfully!\n";
    echo "Result: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
    
    // Check current cart items
    $stmt = $conn->query('SELECT * FROM cart');
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "\n📦 Current cart items: " . count($items) . "\n";
    
    foreach($items as $item) {
        echo "   - ID: {$item['id']}, Product: {$item['product_id']}, Quantity: {$item['quantity']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
